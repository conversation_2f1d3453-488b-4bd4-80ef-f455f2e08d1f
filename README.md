# 陶瓷配料计算器

## 文件结构

项目已经重构为模块化结构，包含以下文件：

### 核心文件

1. **`main.py`** - 主程序入口
   - 程序的启动文件
   - 创建并运行UI界面
   - 包含错误处理

2. **`calculator_core.py`** - 计算核心模块
   - 包含所有计算相关的功能
   - 化学式解析算法
   - 原料数据加载和处理
   - 配料计算逻辑
   - 独立于UI，可以单独使用

3. **`calculator_ui.py`** - 用户界面模块
   - 包含所有UI相关的功能
   - 使用ttkbootstrap创建现代化界面
   - 表格显示和数据输入
   - 结果展示和Excel导出
   - 调用calculator_core进行计算

### 数据文件

4. **`initial.xlsx`** - 原料数据文件
   - 包含所有原料的详细信息
   - 元素、分子式、分子量、纯度等数据

## 运行方法

```bash
python main.py
```

## 模块化优势

1. **代码分离**: UI逻辑和计算逻辑完全分离
2. **易于维护**: 每个模块职责单一，便于修改和调试
3. **可重用性**: 计算核心可以独立使用，不依赖UI
4. **可扩展性**: 可以轻松添加新的UI界面或计算功能
5. **测试友好**: 可以单独测试计算逻辑

## 类结构

- `CeramicCalculatorCore`: 计算核心类
  - 负责化学式解析
  - 原料数据管理
  - 配料计算

- `CeramicCalculatorUI`: 用户界面类
  - 负责界面创建和交互
  - 调用核心类进行计算
  - 结果显示和导出

## 依赖库

- `ttkbootstrap`: 现代化UI界面
- `pandas`: 数据处理和Excel操作
- `openpyxl`: Excel文件读写
- `tkinter`: 基础GUI框架（Python内置）
