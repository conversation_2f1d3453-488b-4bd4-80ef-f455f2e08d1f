import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import re
from typing import Dict
from tkinter import messagebox, filedialog
import json
import os
import pandas as pd
from datetime import datetime

class ChemicalCalculator:
    def __init__(self):
        # 设置主题和颜色模式
        self.window = ttk.Window(themename="litera")
        self.window.title("化学配料计算器")
        self.window.geometry("1400x900")  # 增加默认窗口大小

        # 更新配色方案
        self.colors = {
            'primary': '#4776E6',
            'primary_hover': '#3D63C9',
            'secondary': '#8E54E9',
            'background': '#FFFFFF',
            'surface': '#F8FAFF',
            'border': '#E1E8FF',
            'text': '#2C3E50',
            'text_secondary': '#718096',
            'success': '#48BB78',
            'error': '#F56565',
            'warning': '#ECC94B'
        }

        # 设置窗口背景色和样式
        self.window.configure(bg=self.colors['background'])

        # 设置默认字体
        self.default_font = ("SimSun", 13)  # 宋体
        self.bold_font = ("SimSun", 13, "bold")
        self.large_font = ("SimSun", 24, "bold")
        self.small_font = ("SimSun", 12)

        # 存储所有原料的集合，用于结果表格
        self.all_materials = set()

        # 初始化过量设置
        self.excess_settings = {}

        # 存储原子量数据
        self.atomic_weights = {
            # 第1周期
            'H': 1.00794,    # 氢
            'He': 4.002602,  # 氦

            # 第2周期
            'Li': 6.941,     # 锂
            'Be': 9.012182,  # 铍
            'B': 10.811,     # 硼
            'C': 12.0107,    # 碳
            'N': 14.0067,    # 氮
            'O': 15.999,     # 氧
            'F': 18.9984032, # 氟
            'Ne': 20.1797,   # 氖

            # 第3周期
            'Na': 22.98977,  # 钠
            'Mg': 24.3050,   # 镁
            'Al': 26.981538, # 铝
            'Si': 28.0855,   # 硅
            'P': 30.973762,  # 磷
            'S': 32.065,     # 硫
            'Cl': 35.453,    # 氯
            'Ar': 39.948,    # 氩

            # 第4周期
            'K': 39.0983,    # 钾
            'Ca': 40.078,    # 钙
            'Sc': 44.955910, # 钪
            'Ti': 47.867,    # 钛
            'V': 50.9415,    # 钒
            'Cr': 51.9961,   # 铬
            'Mn': 54.938049, # 锰
            'Fe': 55.845,    # 铁
            'Co': 58.933200, # 钴
            'Ni': 58.6934,   # 镍
            'Cu': 63.546,    # 铜
            'Zn': 65.39,     # 锌
            'Ga': 69.723,    # 镓
            'Ge': 72.64,     # 锗
            'As': 74.92160,  # 砷
            'Se': 78.96,     # 硒
            'Br': 79.904,    # 溴
            'Kr': 83.80,     # 氪

            # 第5周期
            'Rb': 85.4678,   # 铷
            'Sr': 87.62,     # 锶
            'Y': 88.90585,   # 钇
            'Zr': 91.224,    # 锆
            'Nb': 92.90638,  # 铌
            'Mo': 95.94,     # 钼
            'Tc': 98.0,      # 锝
            'Ru': 101.07,    # 钌
            'Rh': 102.90550, # 铑
            'Pd': 106.42,    # 钯
            'Ag': 107.8682,  # 银
            'Cd': 112.411,   # 镉
            'In': 114.818,   # 铟
            'Sn': 118.710,   # 锡
            'Sb': 121.760,   # 锑
            'Te': 127.60,    # 碲
            'I': 126.90447,  # 碘
            'Xe': 131.293,   # 氙

            # 第6周期
            'Cs': 132.90545, # 铯
            'Ba': 137.327,   # 钡
            'La': 138.9055,  # 镧
            'Ce': 140.116,   # 铈
            'Pr': 140.90765, # 镨
            'Nd': 144.24,    # 钕
            'Pm': 145.0,     # 钷
            'Sm': 150.36,    # 钐
            'Eu': 151.964,   # 铕
            'Gd': 157.25,    # 钆
            'Tb': 158.92534, # 铽
            'Dy': 162.50,    # 镝
            'Ho': 164.93032, # 钬
            'Er': 167.259,   # 铒
            'Tm': 168.93421, # 铥
            'Yb': 173.04,    # 镱
            'Lu': 174.967,   # 镥
            'Hf': 178.49,    # 铪
            'Ta': 180.9479,  # 钽
            'W': 183.84,     # 钨
            'Re': 186.207,   # 铼
            'Os': 190.23,    # 锇
            'Ir': 192.217,   # 铱
            'Pt': 195.078,   # 铂
            'Au': 196.96655, # 金
            'Hg': 200.59,    # 汞
            'Tl': 204.3833,  # 铊
            'Pb': 207.2,     # 铅
            'Bi': 208.98040, # 铋
            'Po': 209.0,     # 钋
            'At': 210.0,     # 砹
            'Rn': 222.0,     # 氡

            # 第7周期
            'Fr': 223.0,     # 钫
            'Ra': 226.0,     # 镭
            'Ac': 227.0,     # 锕
            'Th': 232.0381,  # 钍
            'Pa': 231.03588, # 镤
            'U': 238.02891,  # 铀
            'Np': 237.0,     # 镎
            'Pu': 244.0,     # 钚
            'Am': 243.0,     # 镅
            'Cm': 247.0,     # 锔
            'Bk': 247.0,     # 锫
            'Cf': 251.0,     # 锎
            'Es': 252.0,     # 锿
            'Fm': 257.0,     # 镄
            'Md': 258.0,     # 钔
            'No': 259.0,     # 锘
            'Lr': 262.0      # 铹
        }

        # 存储原料信息：(化学式, 纯度)
        self.raw_materials = {
            'Bi': ('Bi2O3', 0.99),
            'Na': ('Na2CO3', 0.99),
            'Ti': ('TiO2', 0.99),
            'Ta': ('Ta2O5', 0.99),
            'Ba': ('BaCO3', 0.99),
            'Ca': ('CaCO3', 0.99),
            'Zr': ('ZrO2', 0.99),
            'Sr': ('SrCO3', 0.99),
            'La': ('La2O3', 0.99),
            'Fe': ('Fe2O3', 0.99)
        }

        # 存储输入框引用
        self.formula_entry = None
        self.target_mass_entry = None

        self.create_widgets()

    def create_widgets(self):
        # 创建主容器
        self.main_container = ttk.Frame(self.window)
        self.main_container.pack(fill="both", expand=True, padx=30, pady=30)

        # 创建顶部标题栏
        self.create_header()

        # 创建内容区域
        content_container = ttk.Frame(self.main_container)
        content_container.pack(fill="both", expand=True, pady=(20, 0))

        # 左侧输入面板
        self.left_panel = ttk.Frame(content_container)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 15))

        # 右侧结果面板
        self.right_panel = ttk.Frame(content_container)
        self.right_panel.pack(side="right", fill="both", expand=True, padx=(15, 0))

        self.create_input_panel()
        self.create_result_panel()

    def create_header(self):
        header = ttk.Frame(self.main_container)
        header.pack(fill="x", pady=(0, 20))

        # Logo和标题
        logo_frame = ttk.Frame(header, bootstyle="primary")
        logo_frame.pack(side="left")

        logo_text = ttk.Label(
            logo_frame,
            text="CC",
            font=self.large_font,
            foreground="white"
        )
        logo_text.pack(expand=True)

        title_frame = ttk.Frame(header)
        title_frame.pack(side="left", padx=15)

        title = ttk.Label(
            title_frame,
            text="化学配料计算器",
            font=self.large_font,
            foreground=self.colors['text']
        )
        title.pack(anchor="w")

        subtitle = ttk.Label(
            title_frame,
            text="精确计算化学配料比例",
            font=self.default_font,
            foreground=self.colors['text_secondary']
        )
        subtitle.pack(anchor="w")

    def create_input_panel(self):
        # 输入区域容器
        input_container = ttk.Frame(self.left_panel)
        input_container.pack(fill="both", expand=True, padx=25, pady=25)

        # 修改为使用更现代的按钮样式
        settings_button = ttk.Button(
            input_container,
            text="原料和过量设置",
            command=self.create_settings_window,
            bootstyle="info outline",  # 改为轮廓样式
            width=20
        )
        settings_button.pack(anchor="e", pady=(0, 15))  # 增加下边距

        # 创建多化学式输入区域
        formulas_frame = ttk.LabelFrame(input_container, text="化学式和目标质量输入", bootstyle="info")
        formulas_frame.pack(fill="x", pady=(0, 20))

        # 创建表头
        header_frame = ttk.Frame(formulas_frame)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))

        formula_header = ttk.Label(
            header_frame,
            text="化学式",
            font=self.bold_font,
            foreground=self.colors['text']
        )
        formula_header.pack(side="left", padx=(5, 0))

        mass_header = ttk.Label(
            header_frame,
            text="目标质量(g)",
            font=self.bold_font,
            foreground=self.colors['text']
        )
        mass_header.pack(side="left", padx=(200, 0))

        # 创建滚动容器
        scroll_container = ttk.Frame(formulas_frame)
        scroll_container.pack(fill="both", expand=True, padx=10, pady=5)

        canvas = ttk.Canvas(scroll_container, height=250)  # 增加高度
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        self.formulas_scrollable_frame = ttk.Frame(canvas)

        # 绑定配置事件
        def update_scrollregion(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.formulas_scrollable_frame.bind("<Configure>", update_scrollregion)

        canvas.create_window((0, 0), window=self.formulas_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加鼠标滚轮支持
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        canvas.bind_all("<MouseWheel>", on_mousewheel)

        # 存储化学式和质量输入框
        self.formula_entries = []
        self.mass_entries = []

        # 添加第一行输入
        self.add_formula_row()

        # 添加按钮区域
        buttons_frame = ttk.Frame(formulas_frame)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))

        # 添加行按钮
        add_row_button = ttk.Button(
            buttons_frame,
            text="+",
            command=self.add_formula_row,
            bootstyle="success-outline",
            width=3
        )
        add_row_button.pack(side="left", padx=(5, 0))

        # 添加提示文本
        hint_label = ttk.Label(
            buttons_frame,
            text="点击添加更多化学式",
            font=self.small_font,
            foreground=self.colors['text_secondary']
        )
        hint_label.pack(side="left", padx=(5, 0))

        # 按钮容器
        button_container = ttk.Frame(input_container)
        button_container.pack(pady=(30, 0))

        # 计算按钮
        self.calculate_button = ttk.Button(
            button_container,
            text="开始计算",
            command=self.calculate_multi,
            bootstyle="primary",
            width=20,
            padding=10
        )
        self.calculate_button.pack(side="left")

    def add_formula_row(self):
        """添加一行化学式和目标质量输入"""
        row_frame = ttk.Frame(self.formulas_scrollable_frame)
        row_frame.pack(fill="x", pady=5)

        # 化学式输入框
        formula_entry = ttk.Entry(
            row_frame,
            font=self.default_font,
            width=30
        )
        formula_entry.pack(side="left", padx=(5, 10))

        # 如果是第一行，添加默认值
        if len(self.formula_entries) == 0:
            formula_entry.insert(0, "0.700Bi0.5Na0.5(Ti0.9000Ta0.0800)O3")

        # 目标质量输入框
        mass_entry = ttk.Entry(
            row_frame,
            font=self.default_font,
            width=10
        )
        mass_entry.pack(side="left", padx=(10, 5))

        # 如果是第一行，添加默认值
        if len(self.mass_entries) == 0:
            mass_entry.insert(0, "40")

        # 删除按钮（第一行不显示）
        if len(self.formula_entries) > 0:
            delete_button = ttk.Button(
                row_frame,
                text="×",
                bootstyle="danger-outline",
                width=3,
                command=lambda f=row_frame, fe=formula_entry, me=mass_entry: self.delete_formula_row(f, fe, me)
            )
            delete_button.pack(side="left", padx=(10, 0))

        # 存储输入框引用
        self.formula_entries.append(formula_entry)
        self.mass_entries.append(mass_entry)

    def create_input_group(self, parent, label_text, helper_text, placeholder):
        frame = ttk.Frame(parent)

        label = ttk.Label(
            frame,
            text=label_text,
            font=self.bold_font,
            foreground=self.colors['text']
        )
        label.pack(anchor="w", pady=(0, 5))

        entry = ttk.Entry(
            frame,
            font=self.default_font,
            foreground=self.colors['text']
        )
        entry.insert(0, placeholder)  # 设置默认值
        entry.pack(fill="x")

        helper = ttk.Label(
            frame,
            text=helper_text,
            font=self.small_font,
            foreground=self.colors['text_secondary']
        )
        helper.pack(anchor="w", pady=(5, 0))

        if label_text == "化学式":
            self.formula_entry = entry
        elif label_text == "目标质量":
            self.target_mass_entry = entry

        return frame

    def create_excess_elements_grid(self, parent):
        # 创建外层容器
        container = ttk.Frame(parent)
        container.pack(fill="x", padx=10, pady=(10, 10))

        # 创建Canvas和Scrollbar
        canvas = ttk.Canvas(container, height=180)  # 减小高度以便更好地显示按钮
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)

        # 创建内部框架来放置元素网格
        grid_frame = ttk.Frame(canvas)

        # 配置Canvas
        canvas.configure(yscrollcommand=scrollbar.set)

        # 获取需要显示过量设置的元素（排除氧元素）
        elements = [elem for elem in self.atomic_weights.keys() if elem != 'O']

        # 在grid_frame中创建元素网格
        row = 0
        col = 0
        for element in elements:
            element_frame = ttk.Frame(grid_frame)
            element_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")  # 增加了水平padding

            self.excess_vars[element] = ttk.BooleanVar()
            cb = ttk.Checkbutton(
                element_frame,
                text=element,
                variable=self.excess_vars[element],
                bootstyle="round-toggle"
            )
            cb.pack(side="left", padx=(0, 5))

            self.excess_entries[element] = ttk.Entry(
                element_frame,
                width=8,  # 增加输入框宽度
                font=self.default_font
            )
            self.excess_entries[element].insert(0, "1.0")  # 设置默认值
            self.excess_entries[element].pack(side="left")

            col += 1
            if col > 3:  # 每行4个元素
                col = 0
                row += 1

        # 创建canvas窗口，添加右侧padding以防止被滚动条遮挡
        canvas.create_window((0, 0), window=grid_frame, anchor="nw")

        # 更新scrollregion
        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 确保grid_frame的宽度比canvas小一些，为滚动条留出空间
            canvas.itemconfig(canvas.find_all()[0], width=event.width-20)  # 减去滚动条宽度

        grid_frame.bind("<Configure>", update_scroll_region)

        # 布局组件
        canvas.pack(side="left", fill="both", expand=True, padx=(0, 20))  # 添加右侧padding
        scrollbar.pack(side="right", fill="y")

        # 添加鼠标滚轮支持
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        canvas.bind_all("<MouseWheel>", on_mousewheel)

        # 确保grid_frame的宽度与canvas相同
        def update_grid_width(event):
            canvas.itemconfig(canvas.find_all()[0], width=event.width)

        canvas.bind("<Configure>", update_grid_width)

    def create_result_panel(self):
        result_container = ttk.Frame(self.right_panel)
        result_container.pack(fill="both", expand=True, padx=25, pady=25)

        # 结果面板标题和按钮区域
        title_frame = ttk.Frame(result_container)
        title_frame.pack(fill="x", pady=(0, 10))

        result_title = ttk.Label(
            title_frame,
            text="计算结果",
            font=self.large_font,
            foreground=self.colors['text']
        )
        result_title.pack(side="left")

        # 创建导出按钮（初始不显示，分析或计算后显示）
        self.export_button = ttk.Button(
            title_frame,
            text="导出Excel",
            command=self.export_to_excel,
            bootstyle="success",
            width=12
        )
        # 注意：按钮初始不显示，只有在有结果时才显示

        # 创建结果表格容器
        self.result_frame = ttk.Frame(result_container)
        self.result_frame.pack(fill="both", expand=True, pady=(10, 0))

        # 创建滚动容器
        canvas = ttk.Canvas(self.result_frame)
        scrollbar_y = ttk.Scrollbar(self.result_frame, orient="vertical", command=canvas.yview)
        scrollbar_x = ttk.Scrollbar(self.result_frame, orient="horizontal", command=canvas.xview)
        self.result_scrollable_frame = ttk.Frame(canvas)

        # 绑定配置事件
        def update_scrollregion(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.result_scrollable_frame.bind("<Configure>", update_scrollregion)

        canvas.create_window((0, 0), window=self.result_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")

        # 添加鼠标滚轮支持
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        canvas.bind_all("<MouseWheel>", on_mousewheel)

        # 保留文本区域用于显示提示信息
        self.result_text = ttk.Text(
            result_container,
            font=self.default_font,
            wrap="word",
            height=5
        )
        self.result_text.pack(fill="x", expand=False, pady=(10, 0))

        # 初始提示信息
        self.result_text.insert("1.0", "请输入化学式和目标质量，然后点击\"开始计算\"按钮。\n")
        self.result_text.insert("end", "计算结果将以表格形式显示，每行对应一个化学式，每列对应一种原料。\n")
        self.result_text.insert("end", "您可以点击\"+\"按钮添加更多化学式。\n")

        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_container, orient="vertical", command=self.result_text.yview)
        scrollbar.pack(side="right", fill="y")
        self.result_text.configure(yscrollcommand=scrollbar.set)

    def parse_chemical_formula(self, formula: str) -> Dict[str, float]:
        """解析化学式"""
        try:
            # 解析化学式并返回元素摩尔数
            def parse_part(part: str, coef: float = 1.0) -> Dict[str, float]:
                """递归解析化学式的一部分"""
                result = {}
                current_element = ''
                current_number = ''
                i = 0

                while i < len(part):
                    char = part[i]

                    if char.isupper():
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = char
                        current_number = ''

                    elif char.islower():
                        current_element += char

                    elif char.isdigit() or char == '.':
                        current_number += char

                    elif char == '(':
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = ''
                        current_number = ''

                        # 找到对应的右括号
                        bracket_count = 1
                        j = i + 1
                        while j < len(part) and bracket_count > 0:
                            if part[j] == '(':
                                bracket_count += 1
                            elif part[j] == ')':
                                bracket_count -= 1
                            j += 1

                        # 获取括号内容
                        inner_content = part[i+1:j-1]

                        # 获取括号后的系数
                        k = j
                        bracket_number = ''
                        while k < len(part) and (part[k].isdigit() or part[k] == '.'):
                            bracket_number += part[k]
                            k += 1

                        bracket_coef = float(bracket_number) if bracket_number else 1.0

                        # 递归处理括号内容
                        inner_result = parse_part(inner_content, bracket_coef)
                        for elem, count in inner_result.items():
                            if elem in result:
                                result[elem] += count
                            else:
                                result[elem] = count

                        i = k - 1

                    i += 1

                # 处理最后一个元素
                if current_element:
                    number = float(current_number) if current_number else 1.0
                    if current_element in result:
                        result[current_element] += number
                    else:
                        result[current_element] = number

                # 应用系数
                return {k: v * coef for k, v in result.items()}

            # 提取主系数
            match = re.match(r'^(\d*\.?\d*)', formula)
            if match and match.group(1):
                main_coef = float(match.group(1))
                formula = formula[len(match.group(1)):]
            else:
                main_coef = 1.0

            # 解析整个化学式
            result = parse_part(formula, main_coef)
            print(f"解析结果: {result}")  # 调试信息
            return result

        except Exception as e:
            print(f"解析错误详情: {str(e)}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            raise ValueError(f"化学式格式错误: {formula}\n详细错误: {str(e)}")

    def calculate_raw_materials(self, element_moles: Dict[str, float], target_mass: float) -> Dict[str, float]:
        """计算原料用量"""
        try:
            raw_material_masses = {}
            total_molar_mass = 0

            print(f"开始计算原料用量...")  # 调试信息
            print(f"元素摩尔数: {element_moles}")  # 调试信息

            for element, moles in element_moles.items():
                if element == 'O':  # 跳过氧元素
                    continue

                if element not in self.raw_materials:
                    raise ValueError(f"未知元素: {element}")

                material, purity = self.raw_materials[element]
                print(f"处理元素 {element}:")  # 调试信息
                print(f"  原料: {material}")  # 调试信息
                print(f"  纯度: {purity}")  # 调试信息

                # 获取过量系数
                try:
                    excess = 1.0
                    if element in self.excess_settings:
                        excess = self.excess_settings[element]
                    print(f"  过量系数: {excess}")  # 调试信息
                except Exception as e:
                    print(f"  获取过量系数出错: {str(e)}")  # 调试信息
                    excess = 1.0

                # 计算原料的摩尔质量
                try:
                    material_molar_mass = self.calculate_compound_mass(material)
                    print(f"  摩尔质量: {material_molar_mass}")  # 调试信息
                except Exception as e:
                    print(f"  计算摩尔质量出错: {str(e)}")  # 调试信息
                    raise

                # 计算质量
                try:
                    mass = moles * material_molar_mass / purity * excess
                    print(f"  计算质量: {mass}")  # 调试信息
                    raw_material_masses[material] = mass
                    total_molar_mass += mass
                except Exception as e:
                    print(f"  计算质量出错: {str(e)}")  # 调试信息
                    raise

            print(f"总摩尔质量: {total_molar_mass}")  # 调试信息
            print(f"目标质量: {target_mass}")  # 调试信息

            # 归一化到目标质量
            try:
                scale_factor = target_mass / total_molar_mass
                print(f"比例因子: {scale_factor}")  # 调试信息
                return {k: v * scale_factor for k, v in raw_material_masses.items()}
            except Exception as e:
                print(f"归一化计算出错: {str(e)}")  # 调试信息
                raise

        except Exception as e:
            print(f"原料计算出错: {str(e)}")  # 调试信息
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")  # 调试信息
            raise ValueError(f"原料计算错误: {str(e)}")

    def calculate_compound_mass(self, compound: str) -> float:
        """计算化合物的摩尔质量"""
        elements = re.findall(r'([A-Z][a-z]*)(\d*)', compound)
        mass = 0
        for element, count in elements:
            count = int(count) if count else 1
            mass += self.atomic_weights[element] * count
        return mass

    # 删除analyze_formula方法，因为我们不再需要它

    def export_to_excel(self):
        """导出计算结果到Excel文件"""
        try:
            if not hasattr(self, 'calculation_results') or 'results' not in self.calculation_results:
                messagebox.showerror("导出错误", "请先计算配料")
                return

            # 创建保存文件对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                initialfile=f"配料计算_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:  # 用户取消了保存
                return

            # 创建Excel写入器
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 创建配料计算数据表格
                # 转置数据结构：行是原料，列是化学式

                # 获取所有化学式和原料
                formulas = [result['formula'] for result in self.calculation_results['results']]
                materials = sorted(self.calculation_results.get('all_materials', []))

                # 创建索引和列（添加总计列）
                index = ['目标质量(g)'] + materials
                columns = formulas + ['总计(g)']

                # 创建数据矩阵
                data = []

                # 添加目标质量行
                mass_row = [result['target_mass'] for result in self.calculation_results['results']]
                # 添加目标质量总和
                total_mass = sum(mass_row)
                mass_row.append(total_mass)
                data.append(mass_row)

                # 计算每种原料的总量
                material_totals = {}
                for material in materials:
                    material_totals[material] = 0
                    for result in self.calculation_results['results']:
                        if material in result['materials']:
                            material_totals[material] += result['materials'][material]

                # 添加原料行
                for material in materials:
                    material_row = []
                    for result in self.calculation_results['results']:
                        if material in result['materials']:
                            material_row.append(round(result['materials'][material], 4))
                        else:
                            material_row.append('-')
                    # 添加原料总量
                    material_row.append(round(material_totals[material], 4))
                    data.append(material_row)

                # 创建DataFrame
                df = pd.DataFrame(data, index=index, columns=columns)

                # 将索引转换为列
                df.reset_index(inplace=True)
                df.rename(columns={'index': '原料/化学式'}, inplace=True)

                # 保存到Excel
                df.to_excel(writer, sheet_name='配料计算', index=False)

                # 添加基本信息
                info_data = {
                    '项目': ['计算时间', '化学式数量'],
                    '值': [
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        len(self.calculation_results['results'])
                    ]
                }

                info_df = pd.DataFrame(info_data)
                info_df.to_excel(writer, sheet_name='基本信息', index=False)

                # 添加原料信息
                materials_info = []
                for element, (formula, purity) in self.raw_materials.items():
                    materials_info.append({
                        '元素': element,
                        '原料': formula,
                        '纯度': purity
                    })

                if materials_info:
                    materials_df = pd.DataFrame(materials_info)
                    materials_df.to_excel(writer, sheet_name='原料信息', index=False)

            messagebox.showinfo("导出成功", f"配料计算结果已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出Excel时出错: {str(e)}")

    def delete_formula_row(self, row_frame, formula_entry, mass_entry):
        """删除一行化学式和目标质量输入"""
        # 从列表中移除输入框引用
        if formula_entry in self.formula_entries:
            idx = self.formula_entries.index(formula_entry)
            self.formula_entries.pop(idx)
            self.mass_entries.pop(idx)

        # 销毁行框架
        row_frame.destroy()

    def calculate_multi(self):
        """执行多个化学式的配料计算"""
        try:
            # 检查是否有输入
            if not self.formula_entries:
                messagebox.showerror("输入错误", "请添加至少一个化学式")
                return

            # 收集所有输入的化学式和目标质量
            formulas = []
            masses = []
            for i, formula_entry in enumerate(self.formula_entries):
                formula = formula_entry.get().strip()
                if not formula:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的化学式不能为空")
                    return

                try:
                    mass = float(self.mass_entries[i].get().strip())
                    if mass <= 0:
                        raise ValueError
                except ValueError:
                    messagebox.showerror("输入错误", f"第 {i+1} 行的目标质量必须是正数")
                    return

                formulas.append(formula)
                masses.append(mass)

            # 存储计算结果用于导出
            self.calculation_results = {
                'formulas': formulas,
                'masses': masses,
                'results': []
            }

            # 清空结果区域
            for widget in self.result_scrollable_frame.winfo_children():
                widget.destroy()

            # 收集所有原料
            all_materials = set()
            all_results = []

            # 计算每个化学式的配料
            for i, (formula, target_mass) in enumerate(zip(formulas, masses)):
                try:
                    element_moles = self.parse_chemical_formula(formula)
                    raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)

                    # 收集所有原料
                    all_materials.update(raw_material_masses.keys())

                    # 存储结果
                    all_results.append({
                        'formula': formula,
                        'target_mass': target_mass,
                        'materials': raw_material_masses
                    })
                except Exception as e:
                    messagebox.showerror("计算错误", f"计算化学式 '{formula}' 时出错: {str(e)}")
                    return

            # 更新计算结果
            self.calculation_results['results'] = all_results
            self.calculation_results['all_materials'] = sorted(list(all_materials))

            # 创建表格
            self.create_result_table(all_results, all_materials)

            # 更新提示信息
            self.result_text.delete("1.0", "end")
            self.result_text.insert("1.0", "计算完成！表格中显示了每个化学式所需的原料质量。\n")
            self.result_text.insert("end", "1. 上述计算结果基于您设置的原料信息\n")
            self.result_text.insert("end", "2. 如果有原料显示为'未设置'，请点击'原料设置'按钮进行配置\n")
            self.result_text.insert("end", "3. 点击'导出Excel'按钮可将计算结果保存为Excel文件\n")

            # 显示导出按钮
            self.export_button.pack(side="right", padx=5)

        except Exception as e:
            messagebox.showerror("意外错误", str(e))

    def create_result_table(self, results, materials):
        """创建结果表格"""
        # 创建表头行（第一行）
        header_frame = ttk.Frame(self.result_scrollable_frame)
        header_frame.pack(fill="x", pady=(0, 5))

        # 添加空白标签作为左上角
        empty_header = ttk.Label(
            header_frame,
            text="原料/化学式",
            font=self.bold_font,
            foreground=self.colors['text'],
            width=20
        )
        empty_header.pack(side="left", padx=5)

        # 添加化学式作为列标题
        for result in results:
            formula_header = ttk.Label(
                header_frame,
                text=result['formula'],
                font=self.bold_font,
                foreground=self.colors['text'],
                width=20
            )
            formula_header.pack(side="left", padx=5)

        # 添加总和列标题
        total_header = ttk.Label(
            header_frame,
            text="总计(g)",
            font=self.bold_font,
            foreground=self.colors['success'],
            width=20
        )
        total_header.pack(side="left", padx=5)

        # 添加分隔线
        separator = ttk.Separator(self.result_scrollable_frame, orient="horizontal")
        separator.pack(fill="x", pady=5)

        # 添加目标质量行
        mass_row_frame = ttk.Frame(self.result_scrollable_frame)
        mass_row_frame.pack(fill="x", pady=2)

        # 添加目标质量标签
        mass_label = ttk.Label(
            mass_row_frame,
            text="目标质量(g)",
            font=self.bold_font,
            foreground=self.colors['text'],
            width=20
        )
        mass_label.pack(side="left", padx=5)

        # 添加每个化学式的目标质量
        total_mass = 0
        for result in results:
            mass = result['target_mass']
            total_mass += mass
            mass_value = ttk.Label(
                mass_row_frame,
                text=f"{mass:.2f}",
                font=self.default_font,
                width=20
            )
            mass_value.pack(side="left", padx=5)

        # 添加目标质量总和
        total_mass_label = ttk.Label(
            mass_row_frame,
            text=f"{total_mass:.2f}",
            font=self.bold_font,
            foreground=self.colors['success'],
            width=20
        )
        total_mass_label.pack(side="left", padx=5)

        # 添加分隔线
        separator2 = ttk.Separator(self.result_scrollable_frame, orient="horizontal")
        separator2.pack(fill="x", pady=5)

        # 计算每种原料的总量
        material_totals = {}
        for material in materials:
            material_totals[material] = 0
            for result in results:
                if material in result['materials']:
                    material_totals[material] += result['materials'][material]

        # 添加原料行
        for material in sorted(materials):
            row_frame = ttk.Frame(self.result_scrollable_frame)
            row_frame.pack(fill="x", pady=2)

            # 添加原料名称
            material_label = ttk.Label(
                row_frame,
                text=material,
                font=self.bold_font,
                foreground=self.colors['text'],
                width=20
            )
            material_label.pack(side="left", padx=5)

            # 添加每个化学式对应的原料质量
            for result in results:
                if material in result['materials']:
                    mass = result['materials'][material]
                    mass_label = ttk.Label(
                        row_frame,
                        text=f"{mass:.4f}",
                        font=self.default_font,
                        width=20
                    )
                else:
                    mass_label = ttk.Label(
                        row_frame,
                        text="-",
                        font=self.default_font,
                        width=20
                    )
                mass_label.pack(side="left", padx=5)

            # 添加原料总量
            total_material = material_totals[material]
            total_material_label = ttk.Label(
                row_frame,
                text=f"{total_material:.4f}",
                font=self.bold_font,
                foreground=self.colors['success'],
                width=20
            )
            total_material_label.pack(side="left", padx=5)

    def calculate(self):
        """执行单个化学式的配料计算（保留向后兼容）"""
        try:
            if not hasattr(self, 'formula_entry') or not hasattr(self, 'target_mass_entry'):
                # 如果没有单个输入框，使用多输入框的第一个
                if not self.formula_entries:
                    messagebox.showerror("输入错误", "请添加至少一个化学式")
                    return

                formula = self.formula_entries[0].get()
                target_mass = float(self.mass_entries[0].get())
            else:
                formula = self.formula_entry.get()
                target_mass = float(self.target_mass_entry.get())

            if not formula:
                messagebox.showerror("输入错误", "请输入化学式")
                return

            element_moles = self.parse_chemical_formula(formula)
            raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)

            # 存储计算结果用于导出
            self.calculation_results = {
                'formulas': [formula],
                'masses': [target_mass],
                'results': [{
                    'formula': formula,
                    'target_mass': target_mass,
                    'materials': raw_material_masses
                }],
                'all_materials': sorted(list(raw_material_masses.keys()))
            }

            # 清空结果区域
            for widget in self.result_scrollable_frame.winfo_children():
                widget.destroy()

            # 创建表格
            self.create_result_table(self.calculation_results['results'], raw_material_masses.keys())

            # 更新提示信息
            self.result_text.delete("1.0", "end")
            self.result_text.insert("1.0", f"化学式 '{formula}' 配料计算结果:\n")
            self.result_text.insert("end", "1. 上述计算结果基于您设置的原料信息\n")
            self.result_text.insert("end", "2. 如果有原料显示为'未设置'，请点击'原料设置'按钮进行配置\n")
            self.result_text.insert("end", "3. 点击'导出Excel'按钮可将计算结果保存为Excel文件\n")

            # 显示导出按钮
            self.export_button.pack(side="right", padx=5)

        except ValueError as e:
            messagebox.showerror("计算错误", str(e))
        except Exception as e:
            messagebox.showerror("意外错误", str(e))

    def run(self):
        self.window.mainloop()

    def create_settings_window(self):
        """创建设置窗口"""
        settings_window = ttk.Toplevel(self.window)
        settings_window.title("原料和过量设置")
        settings_window.geometry("1000x700")  # 增加窗口大小

        # 创建主容器
        main_container = ttk.Frame(settings_window)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建选项卡控件
        tab_control = ttk.Notebook(main_container)
        tab_control.pack(fill="both", expand=True)

        # 创建原料设置选项卡
        materials_tab = ttk.Frame(tab_control)
        tab_control.add(materials_tab, text="原料设置")

        # 创建过量设置选项卡
        excess_tab = ttk.Frame(tab_control)
        tab_control.add(excess_tab, text="元素过量设置")

        # ===== 原料设置选项卡 =====
        # 标题
        title = ttk.Label(
            materials_tab,
            text="原料信息管理",
            font=self.large_font,
            foreground=self.colors['text']
        )
        title.pack(anchor="w", pady=(10, 20))

        # 创建表格容器
        table_container = ttk.Frame(materials_tab)
        table_container.pack(fill="both", expand=True)

        # 创建表头容器
        header_frame = ttk.Frame(table_container)
        header_frame.pack(fill="x", pady=(0, 10))

        # 表头
        headers = ["元素", "原料化学式", "纯度"]
        for i, header in enumerate(headers):
            header_label = ttk.Label(
                header_frame,
                text=header,
                font=self.bold_font,
                foreground=self.colors['text'],
                width=15 if i > 0 else 10
            )
            header_label.pack(side="left", padx=10)

        # 创建滚动容器
        scroll_container = ttk.Frame(table_container)
        scroll_container.pack(fill="both", expand=True)

        canvas = ttk.Canvas(scroll_container)
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # 绑定配置事件
        def update_scrollregion(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", update_scrollregion)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 存储输入框引用
        self.material_entries = {}

        # 添加常用元素的输入行
        common_elements = ['Bi', 'Na', 'Ti', 'Ta', 'Ba', 'Ca', 'Zr', 'Sr', 'La', 'Fe', 'O', 'K', 'Li', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl']

        # 添加所有元素的输入行
        for element in common_elements:
            # 创建元素行容器
            element_row = ttk.Frame(scrollable_frame)
            element_row.pack(fill="x", pady=5)

            # 元素名称
            element_label = ttk.Label(
                element_row,
                text=element,
                font=self.default_font,
                width=10
            )
            element_label.pack(side="left", padx=10)

            # 原料化学式输入框
            formula_entry = ttk.Entry(
                element_row,
                font=self.default_font,
                width=20
            )
            formula_entry.pack(side="left", padx=10)

            # 纯度输入框
            purity_entry = ttk.Entry(
                element_row,
                font=self.default_font,
                width=10
            )
            purity_entry.pack(side="left", padx=10)

            # 如果已有该元素的数据，填入默认值
            if element in self.raw_materials:
                formula, purity = self.raw_materials[element]
                formula_entry.insert(0, formula)
                purity_entry.insert(0, str(purity))

            # 存储输入框引用
            self.material_entries[element] = (formula_entry, purity_entry)

        # 添加分隔线
        separator_frame = ttk.Frame(scrollable_frame)
        separator_frame.pack(fill="x", pady=10)

        separator = ttk.Separator(separator_frame, orient="horizontal")
        separator.pack(fill="x")

        # 添加其他元素标题
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", pady=5)

        other_label = ttk.Label(
            title_frame,
            text="其他元素",
            font=self.bold_font,
            foreground=self.colors['text']
        )
        other_label.pack(anchor="w", padx=10)

        # 添加其他元素
        other_elements = [elem for elem in sorted(self.atomic_weights.keys()) if elem not in common_elements]
        for element in other_elements:
            # 创建元素行容器
            element_row = ttk.Frame(scrollable_frame)
            element_row.pack(fill="x", pady=5)

            # 元素名称
            element_label = ttk.Label(
                element_row,
                text=element,
                font=self.default_font,
                width=10
            )
            element_label.pack(side="left", padx=10)

            # 原料化学式输入框
            formula_entry = ttk.Entry(
                element_row,
                font=self.default_font,
                width=20
            )
            formula_entry.pack(side="left", padx=10)

            # 纯度输入框
            purity_entry = ttk.Entry(
                element_row,
                font=self.default_font,
                width=10
            )
            purity_entry.pack(side="left", padx=10)

            # 如果已有该元素的数据，填入默认值
            if element in self.raw_materials:
                formula, purity = self.raw_materials[element]
                formula_entry.insert(0, formula)
                purity_entry.insert(0, str(purity))

            # 存储输入框引用
            self.material_entries[element] = (formula_entry, purity_entry)

        # 布局滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加鼠标滚轮支持
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        canvas.bind_all("<MouseWheel>", on_mousewheel)

        # ===== 过量设置选项卡 =====
        # 标题
        excess_title = ttk.Label(
            excess_tab,
            text="元素过量设置",
            font=self.large_font,
            foreground=self.colors['text']
        )
        excess_title.pack(anchor="w", pady=(10, 20))

        # 说明文本
        description = ttk.Label(
            excess_tab,
            text="设置元素的过量系数，勾选需要过量的元素并输入过量倍数（例如：1.1表示过量10%）",
            font=self.default_font,
            foreground=self.colors['text_secondary'],
            wraplength=800
        )
        description.pack(anchor="w", pady=(0, 20))

        # 创建过量元素网格
        excess_container = ttk.Frame(excess_tab)
        excess_container.pack(fill="both", expand=True)

        # 创建滚动容器
        excess_scroll_container = ttk.Frame(excess_container)
        excess_scroll_container.pack(fill="both", expand=True)

        excess_canvas = ttk.Canvas(excess_scroll_container)
        excess_scrollbar = ttk.Scrollbar(excess_scroll_container, orient="vertical", command=excess_canvas.yview)
        excess_scrollable_frame = ttk.Frame(excess_canvas)

        # 绑定配置事件
        def update_excess_scrollregion(event):
            excess_canvas.configure(scrollregion=excess_canvas.bbox("all"))

        excess_scrollable_frame.bind("<Configure>", update_excess_scrollregion)

        excess_canvas.create_window((0, 0), window=excess_scrollable_frame, anchor="nw")
        excess_canvas.configure(yscrollcommand=excess_scrollbar.set)

        # 存储过量设置
        self.excess_vars = {}
        self.excess_entries = {}

        # 创建表头
        excess_header_frame = ttk.Frame(excess_scrollable_frame)
        excess_header_frame.pack(fill="x", pady=(0, 10))

        excess_headers = ["元素", "是否过量", "过量倍数"]
        for i, header in enumerate(excess_headers):
            excess_header_label = ttk.Label(
                excess_header_frame,
                text=header,
                font=self.bold_font,
                foreground=self.colors['text'],
                width=15
            )
            excess_header_label.pack(side="left", padx=10)

        # 添加所有元素的过量设置
        all_elements = sorted(self.atomic_weights.keys())
        for element in all_elements:
            if element == 'O':  # 跳过氧元素
                continue

            # 创建元素行容器
            element_row = ttk.Frame(excess_scrollable_frame)
            element_row.pack(fill="x", pady=5)

            # 元素名称
            element_label = ttk.Label(
                element_row,
                text=element,
                font=self.default_font,
                width=15
            )
            element_label.pack(side="left", padx=10)

            # 过量复选框
            self.excess_vars[element] = ttk.BooleanVar()
            excess_cb = ttk.Checkbutton(
                element_row,
                variable=self.excess_vars[element],
                bootstyle="round-toggle"
            )
            excess_cb.pack(side="left", padx=10)

            # 过量倍数输入框
            excess_entry = ttk.Entry(
                element_row,
                font=self.default_font,
                width=10
            )
            excess_entry.insert(0, "1.1")  # 默认过量10%
            self.excess_entries[element] = excess_entry
            excess_entry.pack(side="left", padx=10)

        # 布局滚动组件
        excess_canvas.pack(side="left", fill="both", expand=True)
        excess_scrollbar.pack(side="right", fill="y")

        # 添加鼠标滚轮支持
        def on_excess_mousewheel(event):
            excess_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        excess_canvas.bind_all("<MouseWheel>", on_excess_mousewheel)

        # 底部按钮区域
        button_container = ttk.Frame(main_container)
        button_container.pack(fill="x", pady=(20, 0))

        # 保存按钮
        save_button = ttk.Button(
            button_container,
            text="保存设置",
            command=lambda: self.save_materials_settings(settings_window),
            bootstyle="primary",
            width=15
        )
        save_button.pack(side="right", padx=5)

        # 取消按钮
        cancel_button = ttk.Button(
            button_container,
            text="取消",
            command=settings_window.destroy,
            width=15
        )
        cancel_button.pack(side="right", padx=5)

    def save_materials_settings(self, settings_window):
        """保存原料和过量设置"""
        new_materials = {}

        # 保存原料设置
        for element, (formula_entry, purity_entry) in self.material_entries.items():
            formula = formula_entry.get().strip()
            purity = purity_entry.get().strip()

            if formula and purity:  # 只保存填写了的数据
                try:
                    purity_value = float(purity)
                    if not 0 < purity_value <= 1:
                        raise ValueError
                    new_materials[element] = (formula, purity_value)
                except ValueError:
                    messagebox.showerror(
                        "输入错误",
                        f"元素 {element} 的纯度必须是0到1之间的数字"
                    )
                    return

        # 更新原料数据
        self.raw_materials = new_materials

        # 保存过量设置
        excess_settings = {}
        for element, var in self.excess_vars.items():
            if var.get():  # 如果勾选了过量
                try:
                    excess_value = float(self.excess_entries[element].get().strip())
                    if excess_value <= 0:
                        raise ValueError
                    excess_settings[element] = excess_value
                except ValueError:
                    messagebox.showerror(
                        "输入错误",
                        f"元素 {element} 的过量倍数必须是正数"
                    )
                    return

        # 更新过量设置
        self.excess_settings = excess_settings

        # 保存到文件
        self.save_materials_to_file()
        self.save_excess_to_file()

        messagebox.showinfo("成功", "原料和过量设置已保存")
        settings_window.destroy()

    def save_materials_to_file(self):
        """保存原料数据到文件"""
        try:
            with open('materials.json', 'w', encoding='utf-8') as f:
                json.dump(self.raw_materials, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("保存失败", f"保存原料数据失败：{str(e)}")

    def save_excess_to_file(self):
        """保存过量设置到文件"""
        try:
            with open('excess_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.excess_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("保存失败", f"保存过量设置失败：{str(e)}")

    def load_materials_from_file(self):
        """从文件加载原料数据"""
        try:
            if os.path.exists('materials.json'):
                with open('materials.json', 'r', encoding='utf-8') as f:
                    self.raw_materials = json.load(f)
        except Exception as e:
            messagebox.showerror("加载失败", f"加载原料数据失败：{str(e)}")

    def load_excess_from_file(self):
        """从文件加载过量设置"""
        try:
            if os.path.exists('excess_settings.json'):
                with open('excess_settings.json', 'r', encoding='utf-8') as f:
                    self.excess_settings = json.load(f)
            else:
                self.excess_settings = {}
        except Exception as e:
            messagebox.showerror("加载失败", f"加载过量设置失败：{str(e)}")
            self.excess_settings = {}

if __name__ == "__main__":
    app = ChemicalCalculator()
    app.load_materials_from_file()
    app.load_excess_from_file()
    app.run()




