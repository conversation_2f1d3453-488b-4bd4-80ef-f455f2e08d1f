import re
from typing import Dict
import pandas as pd


class CeramicCalculatorCore:
    """陶瓷配料计算核心类"""
    
    def __init__(self):
        # 原料信息：从Excel文件加载所有行数据
        self.raw_materials_data = []  # 存储所有行的完整数据
        
    def load_materials_from_excel(self, file_path='initial.xlsx'):
        """从Excel文件加载所有原料数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 清空现有数据
            self.raw_materials_data = []

            # 导入所有行的所有列数据
            for index, row in df.iterrows():
                # 将每行数据转换为字典，保留所有列信息
                row_data = {}
                for col in df.columns:
                    value = row[col]
                    # 处理NaN值
                    if pd.isna(value):
                        row_data[col] = ''
                    else:
                        row_data[col] = value

                # 添加行索引
                row_data['_row_index'] = index

                # 存储完整行数据
                self.raw_materials_data.append(row_data)

            print(f"成功从 {file_path} 加载 {len(self.raw_materials_data)} 行完整数据")
            print(f"包含列: {list(df.columns)}")
            return True

        except FileNotFoundError:
            if file_path == 'initial.xlsx':
                print(f"未找到 {file_path} 文件！")
                # 不加载任何数据，让用户手动选择
                self.raw_materials_data = []
                return False
            else:
                raise FileNotFoundError(f"未找到文件: {file_path}")
        except Exception as e:
            if file_path == 'initial.xlsx':
                print(f"加载 {file_path} 时出错: {str(e)}")
                # 不加载任何数据，让用户手动选择
                self.raw_materials_data = []
                return False
            else:
                raise Exception(f"加载 {file_path} 时出错: {str(e)}")

    def parse_chemical_formula(self, formula: str) -> Dict[str, float]:
        """解析化学式"""
        try:
            def parse_part(part: str, coef: float = 1.0) -> Dict[str, float]:
                """递归解析化学式的一部分"""
                result = {}
                current_element = ''
                current_number = ''
                i = 0

                while i < len(part):
                    char = part[i]

                    if char.isupper():
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = char
                        current_number = ''

                    elif char.islower():
                        current_element += char

                    elif char.isdigit() or char == '.':
                        current_number += char

                    elif char == '(':
                        if current_element:
                            number = float(current_number) if current_number else 1.0
                            if current_element in result:
                                result[current_element] += number
                            else:
                                result[current_element] = number
                        current_element = ''
                        current_number = ''

                        # 找到对应的右括号
                        bracket_count = 1
                        j = i + 1
                        while j < len(part) and bracket_count > 0:
                            if part[j] == '(':
                                bracket_count += 1
                            elif part[j] == ')':
                                bracket_count -= 1
                            j += 1

                        # 获取括号内容
                        inner_content = part[i+1:j-1]

                        # 获取括号后的系数
                        k = j
                        bracket_number = ''
                        while k < len(part) and (part[k].isdigit() or part[k] == '.'):
                            bracket_number += part[k]
                            k += 1

                        bracket_coef = float(bracket_number) if bracket_number else 1.0

                        # 递归处理括号内容
                        inner_result = parse_part(inner_content, bracket_coef)
                        for elem, count in inner_result.items():
                            if elem in result:
                                result[elem] += count
                            else:
                                result[elem] = count

                        i = k - 1

                    i += 1

                # 处理最后一个元素
                if current_element:
                    number = float(current_number) if current_number else 1.0
                    if current_element in result:
                        result[current_element] += number
                    else:
                        result[current_element] = number

                # 应用系数
                return {k: v * coef for k, v in result.items()}

            # 提取主系数
            match = re.match(r'^(\d*\.?\d*)', formula)
            if match and match.group(1):
                main_coef = float(match.group(1))
                formula = formula[len(match.group(1)):]
            else:
                main_coef = 1.0

            # 解析整个化学式
            result = parse_part(formula, main_coef)
            return result

        except Exception as e:
            raise ValueError(f"化学式格式错误: {formula}\n详细错误: {str(e)}")

    def get_material_info(self, element):
        """从Excel数据中获取元素的原料信息"""
        for row_data in self.raw_materials_data:
            if row_data.get('Element') == element:
                formula = row_data.get('Formula', '')
                molecular_weight = row_data.get('Molecular Weight', 0)
                purity = row_data.get('Purity', 99.0)
                excess = row_data.get('Excess', 1.0)

                # 处理数据类型
                try:
                    if molecular_weight == '' or pd.isna(molecular_weight):
                        molecular_weight = 0.0
                    else:
                        molecular_weight = float(molecular_weight)

                    if purity == '' or pd.isna(purity):
                        purity = 99.0
                    else:
                        purity = float(purity)
                        if purity > 1:
                            purity = purity / 100

                    if excess == '' or pd.isna(excess):
                        excess = 1.0
                    else:
                        excess = float(excess)

                    if formula == '' or pd.isna(formula):
                        formula = f"{element}2O3"  # 默认氧化物

                except:
                    # 如果转换失败，使用默认值
                    molecular_weight = 100.0
                    purity = 0.99
                    excess = 1.0
                    formula = f"{element}2O3"

                return formula, molecular_weight, purity, excess

        # 如果找不到元素，返回默认值
        return f"{element}2O3", 100.0, 0.99, 1.0

    def calculate_raw_materials(self, element_moles: Dict[str, float], target_mass: float) -> Dict[str, float]:
        """计算原料用量"""
        try:
            raw_material_masses = {}
            total_molar_mass = 0

            for element, moles in element_moles.items():
                if element == 'O':  # 跳过氧元素
                    continue

                # 从Excel数据中获取原料信息
                formula, molecular_weight, purity, excess = self.get_material_info(element)

                if molecular_weight <= 0:
                    raise ValueError(f"元素 {element} 的分子量无效: {molecular_weight}")

                # 计算质量
                mass = moles * molecular_weight / purity * excess
                raw_material_masses[formula] = mass
                total_molar_mass += mass

            # 归一化到目标质量
            if total_molar_mass == 0:
                raise ValueError("计算出的总质量为0，请检查化学式和原料设置")

            scale_factor = target_mass / total_molar_mass
            return {k: v * scale_factor for k, v in raw_material_masses.items()}

        except Exception as e:
            raise ValueError(f"原料计算错误: {str(e)}")

    def calculate_multiple_formulas(self, formulas_and_masses):
        """计算多个化学式的配料"""
        try:
            # 检查是否有原料数据
            if not self.raw_materials_data:
                raise ValueError("没有找到原料数据！请先加载原料数据文件。")

            # 计算每个化学式的配料
            all_results = []
            all_materials = set()

            for formula, target_mass in formulas_and_masses:
                element_moles = self.parse_chemical_formula(formula)
                raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)

                all_materials.update(raw_material_masses.keys())
                all_results.append({
                    'formula': formula,
                    'target_mass': target_mass,
                    'materials': raw_material_masses
                })

            return {
                'results': all_results,
                'all_materials': sorted(list(all_materials))
            }

        except Exception as e:
            raise ValueError(f"计算错误: {str(e)}")
